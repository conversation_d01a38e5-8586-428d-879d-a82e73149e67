package main

import (
	"context"
	"fmt"
	"log"
	"strings"

	"github.com/jackc/pgx/v4/pgxpool"

	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/database"
	server "github.com/izy-mercado/backend/internal/http"
	"github.com/izy-mercado/backend/internal/integrations/storage"
	"github.com/izy-mercado/backend/internal/logger"
)

var version = "dev" // Default if not set via -ldflags //

func main() {
	fmt.Println("App Version:", version)
	// load environment variables
	env := config.Must()
	ctx := context.Background()

	// init postgres connection
	pool := setupPostgres(env.Database, ctx)

	storage, err := storage.New(env.Storage)
	if err != nil {
		log.Fatal("failed to initialize storage:", err)
	}

	// start http server
	server.New(env, pool, storage).Start()
}

// connect to postgres pool
func setupPostgres(dbConfig config.Database, ctx context.Context) *pgxpool.Pool {
	// Use the database URL as-is when SSL certificates are provided
	// The TLS configuration will be handled programmatically below
	databaseURL := dbConfig.URL
	if dbConfig.SSLServerCA != "" {
		// Only add sslmode if not already present in the URL
		if !strings.Contains(databaseURL, "sslmode=") {
			if strings.Contains(databaseURL, "?") {
				databaseURL += "&sslmode=verify-ca"
			} else {
				databaseURL += "?sslmode=verify-ca"
			}
		}
		log.Printf("SSL enabled: using TLS configuration with certificate verification")
	}

	// Parse the database URL
	conf, err := pgxpool.ParseConfig(databaseURL)
	if err != nil {
		log.Fatalf("Unable to parse database url: %v", err)
	}

	// Configure connection pool settings
	conf.MaxConns = 20
	conf.MinConns = 5

	// Configure SSL if server CA certificate is provided
	if dbConfig.SSLServerCA != "" {
		appLogger := logger.NewZerologLogger(log.Writer())
		sslConfig := database.NewSSLConfigFromEnv(dbConfig)

		tlsConfig, err := database.CreateTLSConfig(sslConfig, appLogger)
		if err != nil {
			log.Fatalf("Unable to create TLS config: %v", err)
		}

		if tlsConfig != nil {
			// Set TLS config
			conf.ConnConfig.TLSConfig = tlsConfig

			appLogger.WithFields(map[string]interface{}{
				"component": "database_connection",
				"action":    "ssl_configured",
				"ssl_mode":  "verify-ca",
			}).Info("SSL configuration applied to database connection")
		}
	} else {
		log.Printf("No SSL server CA provided, using standard connection")
	}

	pool, err := pgxpool.ConnectConfig(ctx, conf)
	if err != nil {
		log.Fatalf("Unable to connect to database: %v", err)
	}

	sslStatus := "disabled"
	if dbConfig.SSLServerCA != "" {
		sslStatus = "verify-ca"
	}
	log.Printf("PostgreSQL connection pool established successfully (MaxConns: %d, MinConns: %d, SSL: %s)",
		conf.MaxConns, conf.MinConns, sslStatus)

	return pool
}
