#!/bin/bash

# Install psql if not already installed
if ! command -v psql &> /dev/null; then
    echo "psql not found. Installing PostgreSQL client..."
    apt-get update && apt-get install -y postgresql-client
fi

# Function to setup SSL connection using certificate environment variables
# Creates temporary certificate files for psql and migrate tools
setup_ssl_connection() {
    SSL_PARAMS=""
    TEMP_CERT_DIR=""

    if [ -n "$DATABASE_SSL_SERVER_CA" ]; then
        echo "Setting up SSL connection with verify-ca mode using certificate files..."

        # Create temporary directory for certificates
        TEMP_CERT_DIR=$(mktemp -d)

        # Write server CA certificate to file
        echo "$DATABASE_SSL_SERVER_CA" > "$TEMP_CERT_DIR/server-ca.pem"

        # Only set certificate file parameters (don't override sslmode if already in URL)
        SSL_PARAMS="sslrootcert=$TEMP_CERT_DIR/server-ca.pem"

        # Write client certificates if available
        if [ -n "$DATABASE_SSL_CLIENT_CERT" ] && [ -n "$DATABASE_SSL_CLIENT_KEY" ]; then
            echo "Setting up client certificate authentication..."
            echo "$DATABASE_SSL_CLIENT_CERT" > "$TEMP_CERT_DIR/client-cert.pem"
            echo "$DATABASE_SSL_CLIENT_KEY" > "$TEMP_CERT_DIR/client-key.pem"

            # Set proper permissions for client key
            chmod 600 "$TEMP_CERT_DIR/client-key.pem"

            SSL_PARAMS="$SSL_PARAMS&sslcert=$TEMP_CERT_DIR/client-cert.pem&sslkey=$TEMP_CERT_DIR/client-key.pem"
        fi

        echo "SSL parameters: $SSL_PARAMS"
        echo "Temporary certificates created in: $TEMP_CERT_DIR"
    else
        echo "No SSL server CA provided, using standard connection"
    fi
}

# Function to cleanup temporary certificate files
cleanup_ssl_files() {
    if [ -n "$TEMP_CERT_DIR" ] && [ -d "$TEMP_CERT_DIR" ]; then
        echo "Cleaning up temporary certificate files..."
        rm -rf "$TEMP_CERT_DIR"
    fi
}

# Set trap to cleanup on exit
trap cleanup_ssl_files EXIT

# Setup SSL connection
setup_ssl_connection

# Function to add SSL parameters to a database URL
add_ssl_to_url() {
    local url="$1"
    if [ -n "$SSL_PARAMS" ]; then
        if [[ "$url" == *"?"* ]]; then
            echo "${url}&${SSL_PARAMS}"
        else
            echo "${url}?${SSL_PARAMS}"
        fi
    else
        echo "$url"
    fi
}

# Extract the database name from DATABASE_URL
DB_NAME=$(echo $DATABASE_URL | awk -F'/' '{print $NF}' | awk -F'?' '{print $1}')

# Construct the connection string for the default 'postgres' database
DEFAULT_DB_URL_BASE=$(echo $DATABASE_URL | sed "s|/${DB_NAME}?|/postgres?|" | sed "s|/${DB_NAME}$|/postgres|")
DEFAULT_DB_URL=$(add_ssl_to_url "$DEFAULT_DB_URL_BASE")

# Construct the main database URL with SSL parameters
DATABASE_URL_WITH_SSL=$(add_ssl_to_url "$DATABASE_URL")

# Check if the database exists, and create it if it doesn't
echo "Checking if database '$DB_NAME' exists..."
echo "Using database URL: $DATABASE_URL_WITH_SSL"
if ! psql "${DATABASE_URL_WITH_SSL}" -tc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'" | grep -q 1; then
    echo "Database '$DB_NAME' does not exist. Creating it..."
    psql "${DATABASE_URL_WITH_SSL}" -c "CREATE DATABASE \"$DB_NAME\";"
else
    echo "Database '$DB_NAME' already exists."
fi

# # Import dump only if APP_ENV is development
# if [ "$APP_ENV" = "development" ]; then
#     # Get the last dump from the bkp folder
#     DUMP_PATH=$(find ./bkp -type f -name "db_prod.sql" -printf "%T@ %p\n" | sort -nr | head -n1 | cut -d' ' -f2-)
#     echo "Importing dump from $DUMP_PATH..."
#     psql "$DATABASE_URL_WITH_SSL" < "$DUMP_PATH"
# fi

# Run migrations
echo "Running migrations..."
mkdir -p ./build/bin && \
curl -s -L https://github.com/golang-migrate/migrate/releases/download/v4.14.1/migrate.linux-amd64.tar.gz | tar xz -C ./build/bin/ && \
mv ./build/bin/migrate.linux-amd64 ./build/bin/migrate

# Set environment variables for SSL certificates (migrate tool may use these)
if [ -n "$TEMP_CERT_DIR" ]; then
    export PGSSLROOTCERT="$TEMP_CERT_DIR/server-ca.pem"
    export PGSSLCERT="$TEMP_CERT_DIR/client-cert.pem"
    export PGSSLKEY="$TEMP_CERT_DIR/client-key.pem"
    export PGSSLMODE="verify-ca"
    echo "Set SSL environment variables for migrate tool"
fi

./build/bin/migrate -path ./pkg/storage/postgres/migrations -database "${DATABASE_URL_WITH_SSL}" -verbose up

# Sync sequence to max(id)
echo "Synchronizing products_id_seq with max(id)..."
psql "${DATABASE_URL_WITH_SSL}" -c "SELECT setval('products_id_seq', COALESCE((SELECT MAX(id) FROM products), 0));"
psql "${DATABASE_URL_WITH_SSL}" -c "SELECT setval('categories_id_seq', COALESCE((SELECT MAX(id) FROM categories), 0));"

# Cleanup will be handled by the trap on exit
echo "Migration script completed successfully."